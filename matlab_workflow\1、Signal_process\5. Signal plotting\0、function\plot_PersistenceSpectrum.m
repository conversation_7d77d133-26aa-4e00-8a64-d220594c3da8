function plot_PersistenceSpectrum(signal, fs, position, rows, cols, index, title_text, varargin)
%PLOT_PERSISTENCE_SPECTRUM 肠鸣音信号持久谱图绘制函数
%   计算并绘制信号的持久谱图，专门用于肠鸣音音频信号的频域分析。
%   持久谱图显示信号在不同频率上的功率分布概率，适用于分析信号的
%   频域稳定性和变化特征。该函数提供专业的科研级别可视化。
%
%   语法:
%   plot_persistence_spectrum(signal, fs, position, rows, cols, index, title_text)
%   plot_persistence_spectrum(signal, fs, position, rows, cols, index, title_text, time_range)
%   plot_persistence_spectrum(signal, fs, position, rows, cols, index, title_text, time_range, freq_range)
%   plot_persistence_spectrum(signal, fs, position, rows, cols, index, title_text, time_range, freq_range, overlap_percent)
%
%   输入参数:
%   signal         - 输入信号数据 (数值向量)
%   fs             - 采样频率 (标量，单位：Hz，通常为2570Hz)
%   position       - 图形窗口位置 ([left, bottom, width, height])
%   rows           - 子图行数 (正整数)
%   cols           - 子图列数 (正整数)
%   index          - 当前子图索引 (正整数)
%   title_text     - 图形标题 (字符串)
%   time_range     - 可选，时间轴范围 ([t_min, t_max]，单位：秒)
%   freq_range     - 可选，频率轴范围 ([f_min, f_max]，单位：Hz)
%   overlap_percent- 可选，重叠百分比 (0-99，默认50)
%
%   输出参数:
%   无 - 直接生成持久谱图显示
%
%   技术参数:
%   - 默认频率范围: [0, 1285] Hz (适合肠鸣音分析)
%   - 默认时间范围: 整个信号长度
%   - 默认重叠百分比: 50%
%   - 泄漏参数: 0.85 (平滑谱图)
%   - Y轴范围: [-120, -35] dB
%   - 字体设置: Times New Roman, 加粗, 16pt刻度, 18pt标签
%
%   图形特征:
%   - X轴: 频率 (Hz)
%   - Y轴: 功率谱密度 (dB)
%   - 颜色映射: 持久性概率分布
%   - 网格: 开启，便于读数
%
%   持久谱图特性:
%   - 显示信号在各频率上的功率分布概率
%   - 颜色深浅表示该频率-功率组合出现的概率
%   - 适用于分析信号的频域稳定性
%   - 能够识别间歇性频率成分
%
%   应用场景:
%   - 肠鸣音信号频域稳定性分析
%   - 间歇性频率成分识别
%   - 信号质量评估
%   - 频域特征的统计分析
%
%   使用示例:
%   % 基本调用
%   plot_persistence_spectrum(signal, 2570, [100,100,800,600], 1, 1, 1, 'Persistence Spectrum');
%
%   % 指定时间范围
%   plot_persistence_spectrum(signal, 2570, [100,100,800,600], 2, 1, 1, 'Persistence Spectrum', [0, 60]);
%
%   % 指定时间和频率范围
%   plot_persistence_spectrum(signal, 2570, [100,100,800,600], 2, 1, 1, 'Persistence Spectrum', [0, 60], [0, 1000]);
%
%   % 指定所有参数
%   plot_persistence_spectrum(signal, 2570, [100,100,800,600], 2, 1, 1, 'Persistence Spectrum', [0, 60], [0, 1000], 75);
%
%   注意事项:
%   - 输入信号应为一维数值向量
%   - 采样频率必须为正数
%   - 时间范围和频率范围应为递增的二元素向量
%   - 重叠百分比应在0-99之间
%   - 需要足够长的信号以获得有意义的持久谱
%
%   错误处理:
%   - 输入参数类型和维度验证
%   - 采样频率有效性检查
%   - 时间和频率范围合理性验证
%   - 重叠百分比范围检查
%
%   参见: PSPECTRUM, PLOT_WAVEFORM, PLOT_SPECTROGRAM, TIMETABLE
%
%   作者: [医学信号处理团队]
%   创建日期: 2025-08-26
%   最后修改: 2025-08-26
%   版本: 2.0

    % 清理工作区变量，避免维度冲突问题
    try
        % 清理可能存在的临时变量
        if exist('P', 'var'), clear P; end
        if exist('F', 'var'), clear F; end
        if exist('PWR', 'var'), clear PWR; end
        if exist('tt', 'var'), clear tt; end
        if exist('tt_ROI', 'var'), clear tt_ROI; end
    catch
        % 忽略清理过程中的任何错误
    end

    % 输入参数验证
    if nargin < 7
        error('plot_persistence_spectrum:NotEnoughInputs', '至少需要7个输入参数');
    end

    % 验证信号数据
    if ~isnumeric(signal) || ~isvector(signal)
        error('plot_persistence_spectrum:InvalidSignal', '信号数据必须是数值向量');
    end

    % 验证采样频率
    if ~isnumeric(fs) || ~isscalar(fs) || fs <= 0
        error('plot_persistence_spectrum:InvalidSamplingRate', '采样频率必须是正数标量');
    end

    % 验证位置参数
    if ~isnumeric(position) || length(position) ~= 4
        error('plot_persistence_spectrum:InvalidPosition', '位置参数必须是4元素数值向量');
    end

    % 验证子图参数
    if ~isnumeric(rows) || ~isscalar(rows) || rows <= 0 || rows ~= round(rows)
        error('plot_persistence_spectrum:InvalidRows', '行数必须是正整数');
    end
    if ~isnumeric(cols) || ~isscalar(cols) || cols <= 0 || cols ~= round(cols)
        error('plot_persistence_spectrum:InvalidCols', '列数必须是正整数');
    end
    if ~isnumeric(index) || ~isscalar(index) || index <= 0 || index ~= round(index)
        error('plot_persistence_spectrum:InvalidIndex', '索引必须是正整数');
    end

    % 验证标题
    if ~ischar(title_text) && ~isstring(title_text)
        error('plot_persistence_spectrum:InvalidTitle', '标题必须是字符串');
    end

    % 处理可选参数
    time_range = [];
    freq_range = [0, 1285]; % 默认频率范围，适合肠鸣音分析
    overlap_percent = 50; % 默认重叠百分比

    if nargin >= 8 && ~isempty(varargin{1})
        time_range = varargin{1};
        if ~isnumeric(time_range) || length(time_range) ~= 2 || time_range(1) >= time_range(2)
            error('plot_persistence_spectrum:InvalidTimeRange', '时间范围必须是递增的二元素向量');
        end
    end

    if nargin >= 9 && ~isempty(varargin{2})
        freq_range = varargin{2};
        if ~isnumeric(freq_range) || length(freq_range) ~= 2 || freq_range(1) >= freq_range(2)
            error('plot_persistence_spectrum:InvalidFreqRange', '频率范围必须是递增的二元素向量');
        end
    end

    if nargin >= 10 && ~isempty(varargin{3})
        overlap_percent = varargin{3};
        if ~isnumeric(overlap_percent) || ~isscalar(overlap_percent) || overlap_percent < 0 || overlap_percent >= 100
            error('plot_persistence_spectrum:InvalidOverlap', '重叠百分比必须在0-99之间');
        end
    end

    % 确保信号为列向量
    signal = signal(:);

    % 计算信号时间向量
    N = length(signal);
    t = (0:N-1) / fs;

    % 将信号转换为timetable格式（pspectrum持久谱需要）
    % 修复：使用与chijiuputu.m一致的timetable创建方式
    try
        % 创建时间向量（以秒为单位）
        time_seconds = seconds(t');

        % 创建timetable，确保数据结构与chijiuputu.m一致
        tt = timetable(time_seconds, signal, 'VariableNames', {'data'});

        % 清理临时变量，避免工作区变量维度冲突
        clear time_seconds;

    catch ME
        error('plot_PersistenceSpectrum:TimetableError', '创建timetable失败: %s', ME.message);
    end

    % 处理时间范围 - 使用与chijiuputu.m完全一致的方式
    if ~isempty(time_range)
        try
            % 使用与chijiuputu.m相同的timerange处理方式
            time_limits = seconds(time_range);
            tt_ROI = tt(:,'data');  % 先选择data列
            tt_ROI = tt_ROI(timerange(time_limits(1), time_limits(2), 'closed'), 1);  % 再应用时间范围

            % 清理临时变量
            clear time_limits;

        catch ME
            error('plot_PersistenceSpectrum:TimeRangeError', '时间范围处理失败: %s', ME.message);
        end
    else
        % 如果没有时间范围限制，也要保持与chijiuputu.m一致的数据结构
        tt_ROI = tt(:,'data');
    end

    % 创建图形和子图 - 改进图形句柄管理
    fig_handle = figure('Position', position, 'Visible', 'on');
    ax_handle = subplot(rows, cols, index);

    % 确保当前轴处于活动状态
    axes(ax_handle);
    hold on;

    % 计算并绘制持久谱图 - 使用与chijiuputu.m完全一致的方法
    try
        % 使用与chijiuputu.m相同的调用方式：带输出参数
        [P, F, PWR] = pspectrum(tt_ROI, ...
            'persistence', ...
            'FrequencyLimits', freq_range, ...
            'OverlapPercent', overlap_percent);

        % 检查输出数据的有效性
        if ~isempty(P) && ~isempty(F) && ~isempty(PWR)
            % 手动绘制持久谱图，确保显示稳定
            imagesc(ax_handle, F, PWR, P');
            axis(ax_handle, 'xy');

            % 添加颜色条
            colorbar(ax_handle);
            colormap(ax_handle, 'parula');

            % 设置坐标轴范围
            xlim(ax_handle, freq_range);
            ylim(ax_handle, [-120, -35]);

        else
            error('持久谱数据为空');
        end

    catch ME
        % 备用方法：尝试不带输出参数的调用
        try
            fprintf('警告: 带输出参数方法失败，尝试直接绘图方法...\n');

            % 清除当前轴内容
            cla(ax_handle);

            % 直接调用pspectrum进行绘图
            pspectrum(tt_ROI, ...
                'persistence', ...
                'FrequencyLimits', freq_range, ...
                'OverlapPercent', overlap_percent);

            % 获取当前轴句柄并设置属性
            ax_handle = gca;
            xlim(ax_handle, freq_range);
            ylim(ax_handle, [-120, -35]);

        catch ME2
            % 最后的备用方案：显示错误信息
            cla(ax_handle);
            text(ax_handle, 0.5, 0.5, {'持久谱图计算失败', '数据可能不足或参数不当', ['错误: ' ME2.message]}, ...
                'Units', 'normalized', ...
                'HorizontalAlignment', 'center', ...
                'VerticalAlignment', 'middle', ...
                'FontSize', 12, 'Color', 'red');
            xlim(ax_handle, freq_range);
            ylim(ax_handle, [-120, -35]);
        end
    end

    % 设置图形属性 - 优化变量管理，避免工作区冲突
    try
        % 获取当前轴句柄，避免使用gca可能导致的句柄混乱
        current_ax = ax_handle;

        % 设置字体和样式属性
        set(current_ax, 'FontSize', 16, 'FontName', 'Times New Roman', 'FontWeight', 'bold');

        % 确保坐标轴范围设置正确
        xlim(current_ax, freq_range);
        ylim(current_ax, [-120, -35]); % 适合肠鸣音信号的功率范围

        % 设置标签和标题
        xlabel(current_ax, 'Frequency (Hz)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
        ylabel(current_ax, 'Power Spectral Density (dB)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
        title(current_ax, title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');

        % 开启网格
        grid(current_ax, 'on');
        set(current_ax, 'GridAlpha', 0.3);

        % 确保图形保持可见和稳定
        set(fig_handle, 'Visible', 'on');

        % 强制刷新并保持图形窗口
        drawnow;
        pause(0.1); % 短暂暂停确保图形完全渲染

    catch ME
        warning('plot_PersistenceSpectrum:GraphicsError', '图形属性设置失败: %s', ME.message);
    end

    % 清理临时变量，避免工作区变量维度冲突
    try
        clear t N time_seconds time_limits current_ax;
        % 注意：不清理fig_handle和ax_handle，因为它们可能在函数外部需要使用
    catch
        % 忽略清理过程中的任何错误
    end

end